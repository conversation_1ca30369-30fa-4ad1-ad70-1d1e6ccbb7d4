<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - foxBase PWA</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        .container {
            max-width: 400px;
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        h1 {
            margin: 0 0 20px 0;
            font-size: 2em;
        }
        p {
            margin: 0 0 30px 0;
            opacity: 0.9;
            line-height: 1.6;
        }
        .icon {
            font-size: 4em;
            margin-bottom: 20px;
        }
        .retry-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .retry-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .status {
            margin-top: 20px;
            font-size: 14px;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📱</div>
        <h1>Режим Offline</h1>
        <p>Приложение работает без интернета.<br>
        Некоторые функции могут быть ограничены.</p>
        
        <button class="retry-btn" onclick="checkConnection()">
            Проверить соединение
        </button>
        
        <div class="status" id="status">
            Service Worker активен
        </div>
    </div>

    <script>
        function checkConnection() {
            const status = document.getElementById('status');
            status.textContent = 'Проверка соединения...';
            
            fetch('/fx/base/manifest.json', { cache: 'no-cache' })
                .then(() => {
                    status.textContent = 'Соединение восстановлено!';
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                })
                .catch(() => {
                    status.textContent = 'Все еще offline';
                });
        }

        // Автоматическая проверка каждые 30 секунд
        setInterval(checkConnection, 30000);
        
        // Показываем информацию о Service Worker
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(registration => {
                document.getElementById('status').textContent = 
                    `SW активен (v${registration.active?.scriptURL.split('?')[1] || 'unknown'})`;
            });
        }
    </script>
</body>
</html>
